const {
	shipment_room: room,
	room_iso_code,
	shipment_inventory,
	sequelize
} = require("../../database/schemas");
const { Op, literal } = require("sequelize");
const StaffModel = require("../../models/Admin/staffModel");

exports.fetchRoomListForCompnay = async (request) => {
	return await room.findAll({
		where: {
			company_id: "-1",
			status: "active",
		},
	});
}

exports.findOldStatusRoom = async (roomId) => {
	return room.findOne({
		where: {
			shipment_room_id: roomId
		}
	})
}

exports.checkValidRoomModel = async (body) => {
	return await
		room.findOne({
			where: {
				shipment_room_id: body.room_id
			}
		})

}

exports.createRoomListForCompnay = async (fetchRoomListForCompnay, companyDetails) => {
	let newArrayData = [];
	for (let i = 0; i < fetchRoomListForCompnay.length; i++) {
		let container = {};
		container.name = fetchRoomListForCompnay[i].name;
		container.admin_id = null;
		container.company_id = companyDetails.company_id;
		container.staff_id = null;
		container.status = fetchRoomListForCompnay.status;
		newArrayData.push(container)
	}
	return await room.bulkCreate(newArrayData);
}



exports.getRoomListingModel = async (fieldsAndValues, userDetails) => {



	if (userDetails.admin_id !== null) {
		return await room.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				admin_id: {
					[Op.not]: null,
				},
				[Op.or]: [
					{ shipment_room_id: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
					{ name: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
				],
				status: fieldsAndValues.filter ? fieldsAndValues.filter : "Active",
			},
			attributes: [
				"shipment_room_id",
				"name",
				"status",
				"admin_id",
				"company_id",
				"staff_id",
				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where room_id = shipment_room.shipment_room_id and deletedAt IS NULL)'
					),
					"totalItems",
				],
			],
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "name",
					fieldsAndValues.order_sequence ? fieldsAndValues.order_sequence : "ASC",
				],
			],
		});
	}

	else if (userDetails.staff_id !== null) {

		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(userDetails.staff_id);


		return await room.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,

			where: {
				[Op.or]: [
					{ staff_id: userDetails.staff_id },
					{ company_id: getStaffDetails.company_id },
				],
				name:
				{
					[Op.like]: "%" + fieldsAndValues.search + "%"
				}
				,
				status: fieldsAndValues.filter ? fieldsAndValues.filter : "active",
			},

			attributes: [
				"shipment_room_id",
				"name",
				"status",
				"admin_id",
				"company_id",
				"staff_id",
				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where room_id = shipment_room.shipment_room_id and deletedAt IS NULL)'
					),
					"totalItems",
				],
			],
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "name",
					fieldsAndValues.order_sequence ? fieldsAndValues.order_sequence : "ASC",
				],
			],
		});
	}

	else {
		return await room.findAndCountAll({
			limit: fieldsAndValues.page_size ? parseInt(fieldsAndValues.page_size) : 10,
			offset:
				fieldsAndValues.page_no > 1
					? (parseInt(fieldsAndValues.page_no) - 1) * fieldsAndValues.page_size
					: 0,
			where: {
				company_id: userDetails.company_id,
				[Op.or]: [
					{ shipment_room_id: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
					{ name: { [Op.like]: "%" + fieldsAndValues.search + "%" } },
				],
				status: fieldsAndValues.filter ? fieldsAndValues.filter : "Active",
			},
			attributes: [
				"shipment_room_id",
				"name",
				"status",
				"admin_id",
				"company_id",
				"staff_id",
				[
					sequelize.literal(
						'(select count(shipment_inventory_id) FROM `shipment_inventories` where room_id = shipment_room.shipment_room_id and deletedAt IS NULL)'
					),
					"totalItems",
				],
			],
			order: [
				[
					fieldsAndValues.order_by_fields
						? fieldsAndValues.order_by_fields
						: "name",
					fieldsAndValues.order_sequence ? fieldsAndValues.order_sequence : "ASC",
				],
			],
		});

	}
}

exports.createRoomModelForAddItem = async (roomName, getUserDetails, add_permanently) => {
	if (getUserDetails.admin_id !== null) {
		const Roomdetials = await room.create({
			name: roomName,
			admin_id: getUserDetails.admin_id,
			company_id: (add_permanently == false || add_permanently == "false") ? null : "-1",
			staff_id: getUserDetails.staff_id
		});
		return Roomdetials;
	}
	else if (getUserDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);
		const Roomdetials = await room.create({
			name: roomName,
			admin_id: getUserDetails.admin_id,
			company_id: (add_permanently == false || add_permanently == "false") ? null : getStaffDetails.company_id,
			staff_id: getUserDetails.staff_id
		});
		return Roomdetials;
	}
	else {
		const Roomdetials = await room.create({
			name: roomName,
			admin_id: getUserDetails.admin_id,
			company_id: (add_permanently == false || add_permanently == "false") ? null : getUserDetails.company_id,
			staff_id: getUserDetails.staff_id
		});
		return Roomdetials;
	}
}

exports.createRoomModel = async (roomName, admin_id, company_id, staff_id, getUserDetails) => {


	if (getUserDetails.admin_id !== null) {
		let Roomdetials = await room.create({
			name: roomName,
			admin_id: getUserDetails.admin_id,
			company_id: "-1",
			staff_id: getUserDetails.staff_id
		});
		return Roomdetials
	}
	else if (getUserDetails.staff_id !== null) {
		let getStaffDetails = await StaffModel.getStaffDetailsForCompnay(getUserDetails.staff_id);
		let Roomdetials = await room.create({
			name: roomName,
			admin_id: getUserDetails.admin_id,
			company_id: getStaffDetails.company_id,
			staff_id: getUserDetails.staff_id
		});
		return Roomdetials
	}
	else {
		let Roomdetials = await room.create({
			name: roomName,
			admin_id: getUserDetails.admin_id,
			company_id: getUserDetails.company_id,
			staff_id: getUserDetails.staff_id
		});
		return Roomdetials
	}
}

exports.editRoomModel = async (roomId, roomName) =>
	await room.update(
		{ name: roomName },
		{ where: { shipment_room_id: roomId } }
	);
exports.changeShipmentRoomStatus = async (roomId) =>
	await room.update(
		{
			status: literal(
				'CASE WHEN status = "Active" THEN "Inactive" ELSE "Active" END'
			),
		},
		{ where: { shipment_room_id: roomId } }
	);

exports.getRoomModel = async (roomId) => await room.findByPk(roomId);

exports.getRoomViaNameModel = async (roomName, details) => {
	return room.findOne({
		where:
		{
			name: roomName,
			staff_id: details.staff_id
		}
	});
}

exports.roomIsoCode = async (room) => {
	return room_iso_code.findOne({
		where:
		{
			name: room,
		}
	});
}

exports.removeRoomModel = async (roomId) =>
	await room.destroy({ where: { shipment_room_id: roomId } });

exports.checkRoomExistenceModel = async (roomId) => {
	const isRoom = await room
		.findOne({
			where: { shipment_room_id: roomId },
			attributes: ["shipment_room_id"],
			raw: true,
		});
	if (isRoom !== null) {
		return true
	}
	else {
		return false
	}
}

exports.checkRoomAssignToJob = async (id) => {
	return await shipment_inventory.findAndCountAll({
		where: {
			room_id: id,
		},
	});
};

exports.batchRoomListStatusChange = async (roomList, isActiveFlag) =>
	await room.update(
		{
			status: isActiveFlag ? "Active" : "Inactive"
		},
		{ where: { shipment_room_id: { [Op.in]: roomList } } }
	);

exports.batchDeleteRoomListStatus = async (roomList) =>
	await room.destroy(
		{ where: { shipment_room_id: { [Op.in]: roomList } } }
	);

exports.checkRoomAssignToItems = async (roomList) => {
	const uniqueRoomIds = await shipment_inventory.findAll({
		attributes: [
			[sequelize.fn('DISTINCT', sequelize.col('room_id')), 'room_id']
		],
		where: {
			room_id: { [Op.in]: roomList }
		},
		raw: true
	});
	return uniqueRoomIds.map((item) => item.room_id);
};

exports.updateRoomIsoCode = async (roomId, isoCode) => {
	return await room.update(
		{ iso_code: isoCode },
		{ where: { shipment_room_id: roomId } }
	);
};
